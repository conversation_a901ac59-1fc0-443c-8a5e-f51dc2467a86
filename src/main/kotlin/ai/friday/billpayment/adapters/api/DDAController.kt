package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.DDABillTO
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import org.slf4j.LoggerFactory

@Controller("/dda")
@Secured(SecurityRule.IS_AUTHENTICATED)
class DDAController(
    private val messagePublisher: MessagePublisher,
    private val sqsConfiguration: SQSMessageHandlerConfiguration,
) {
    private val logger = LoggerFactory.getLogger(DDAController::class.java)

    @Post
    fun receiveDDABill(@Body ddaBillTO: DDABillTO): HttpResponse<Map<String, String>> {
        return try {
            logger.info("Received DDA bill: barcode=${ddaBillTO.numcodbarras}, document=${ddaBillTO.cnpj_cpf_sacado}")

            // Enviar para a fila que o SQSDDABillsHandler processa
            messagePublisher.sendMessage(
                queueName = sqsConfiguration.ddaBills,
                body = ddaBillTO
            )

            logger.info("DDA bill sent to queue successfully: barcode=${ddaBillTO.numcodbarras}")

            HttpResponse.ok(mapOf("status" to "success", "message" to "DDA bill received and queued"))
        } catch (e: Exception) {
            logger.error("Error processing DDA bill: barcode=${ddaBillTO.numcodbarras}", e)
            HttpResponse.serverError(mapOf("status" to "error", "message" to "Failed to process DDA bill"))
        }
    }
}
