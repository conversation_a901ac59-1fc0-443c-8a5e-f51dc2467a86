import com.adarshr.gradle.testlogger.TestLoggerExtension
import com.adarshr.gradle.testlogger.theme.ThemeType

plugins {
    kotlin("jvm") version "1.9.21"
    id("org.jetbrains.kotlin.plugin.allopen") version "1.9.22"
    id("io.micronaut.application") version "4.5.3"
    id("org.jetbrains.kotlin.kapt") version "1.9.22"
    id("com.github.johnrengelman.shadow") version "8.1.1"
    id("org.jlleitschuh.gradle.ktlint") version "11.6.1"
    id("com.adarshr.test-logger") version "4.0.0" // plugin para mostrar os testes que estão sendo executados
    id("java-test-fixtures")
    id("jacoco")
    id("org.gradle.test-retry") version "1.5.9"
}

version = "0.1"
group = "ai.friday.billpayment"

val kotlinVersion = project.properties["kotlinVersion"]
val isCiServer = providers.environmentVariable("CI_JOB_TOKEN").isPresent

val maxParallelForksValue = if (providers.environmentVariable("BP_MAX_PARALLEL_FORKS").isPresent) {
    providers.environmentVariable("BP_MAX_PARALLEL_FORKS").get().toInt()
} else if (isCiServer) {
    4
} else {
    2
}

println("isCiServer: $isCiServer")
println("maxParallelForksValue: $maxParallelForksValue")
println("getMaxWorkerCount: ${project.gradle.startParameter.maxWorkerCount}")

subprojects {
    apply(plugin = "kotlin")
    apply(plugin = "org.jetbrains.kotlin.plugin.allopen")
    apply(plugin = "io.micronaut.application")
    apply(plugin = "org.jetbrains.kotlin.kapt")
    apply(plugin = "org.jlleitschuh.gradle.ktlint")
    apply(plugin = "com.adarshr.test-logger")
    apply(plugin = "jacoco")

    repositories {
        mavenLocal()
        mavenCentral()
        maven {
            url = uri("https://gitlab.com/api/v4/groups/6318341/-/packages/maven")
            if (isCiServer) {
                credentials(HttpHeaderCredentials::class) {
                    name = "Job-Token"
                    value = providers.environmentVariable("CI_JOB_TOKEN").get()
                }
            } else {
                credentials(HttpHeaderCredentials::class) {
                    name = "Private-Token"
                    value =
                        if (providers.environmentVariable("GITLAB_KEY").isPresent) { // Adicione a sua chave de acesso ao Gitlab em uma variavel de ambiente chamada GITLAB_KEY
                            providers.environmentVariable("GITLAB_KEY").get()
                        } else {
                            ""
                        }
                }
            }

            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }

    tasks {
        compileKotlin {
            incremental = true
            compilerOptions.jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17
            kotlinOptions.jvmTarget = "17"
        }
        compileTestKotlin {
            incremental = true
            compilerOptions.jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17
            kotlinOptions.jvmTarget = "17"
        }

        test {
            jvmArgs =
                listOf(
                    "-Xmx2g",
                    "-Djava.locale.providers=COMPAT,CLDR",
                    "--add-opens",
                    "java.base/java.util=ALL-UNNAMED",
                )
            // os subprojetos estao executando em paralelo (org.gradle.parallel=true no arquivo gradle.properties)
            // e mais de um fork custa caro para executar poucos testes (cada fork sobe uma jvm e dois containers, amazon/dynamodb-local:latest e testcontainers/ryuk:0.5.1)
            // mas vale manter mais de um fork no modulo principal
            maxParallelForks = 1 // maxParallelForksValue

            useJUnitPlatform()
        }

        jar {
            isZip64 = true
        }

        configure<TestLoggerExtension> {
            theme = ThemeType.STANDARD_PARALLEL
            showPassed = false
            showSkipped = false
            showOnlySlow = false
        }
    }
}

repositories {
    mavenLocal()
    mavenCentral()
    maven {
        url = uri("https://gitlab.com/api/v4/groups/6318341/-/packages/maven")
        if (providers.environmentVariable("CI_JOB_TOKEN").isPresent) { // TODO - como melhorar isso?
            credentials(HttpHeaderCredentials::class) {
                name = "Job-Token"
                value = providers.environmentVariable("CI_JOB_TOKEN").get()
            }
        } else {
            credentials(HttpHeaderCredentials::class) {

                val token = providers.environmentVariable("GITLAB_KEY").orNull
                    ?: findProperty("GITLAB_KEY")?.toString()

                token?.let { t -> println("GITLAB_KEY FOUND: length/${t.length} last 3 chars/${t.takeLast(3)}") }

                name = "Private-Token"
                value = token.orEmpty()
            }
        }

        authentication {
            create<HttpHeaderAuthentication>("header")
        }
    }
}

dependencies {

    // dependencias base - geradas com o Micronaut launch
    // ksp("io.micronaut:micronaut-http-validation")
    // ksp("io.micronaut.security:micronaut-security-annotations")
    // ksp("io.micronaut.validation:micronaut-validation-processor")
    kapt("io.micronaut:micronaut-http-validation")
    kapt("io.micronaut.security:micronaut-security-annotations")
    kapt("io.micronaut.validation:micronaut-validation-processor")
    api("io.micronaut.validation:micronaut-validation")
    api("jakarta.validation:jakarta.validation-api")
    api("io.micronaut:micronaut-http-client")
    api("io.micronaut:micronaut-jackson-databind")
    api("io.micronaut:micronaut-management")
    api("io.micronaut.cache:micronaut-cache-caffeine")
    api("io.micronaut.kotlin:micronaut-kotlin-runtime")
    api("io.micronaut.micrometer:micronaut-micrometer-registry-statsd")
    api("io.micronaut.rxjava2:micronaut-rxjava2-http-client")
    api("io.micronaut.security:micronaut-security-jwt")
    api("io.micronaut.validation:micronaut-validation")
    api("org.jetbrains.kotlin:kotlin-reflect:$kotlinVersion")
    api("org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlinVersion")
    api("software.amazon.awssdk:dynamodb")
    api("ch.qos.logback:logback-classic")
    api("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.yaml:snakeyaml")
    // aotPlugins(platform("io.micronaut.platform:micronaut-platform:4.8.2"))
    // aotPlugins("io.micronaut.security:micronaut-security-aot")

    // dependencias exigidas em tempo de compilação
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j:1.7.3")
    implementation("io.via1:communication-centre:2.47")
    api("ai.friday.morning:morning-date:0.9.10")
    api("ai.friday.morning:morning-messaging:0.9.10")
    api("software.amazon.awssdk:cognitoidentityprovider")
    api("software.amazon.awssdk:dynamodb-enhanced")
    api("com.amazonaws:aws-java-sdk-dynamodb") // ao invés de pegar do shedlock
    api("io.arrow-kt:arrow-core:1.2.0")
    api("net.javacrumbs.shedlock:shedlock-micronaut:5.8.0") // ao invés de 4.33.0"
    api("net.javacrumbs.shedlock:shedlock-provider-dynamodb2:5.8.0") // ao invés de "net.javacrumbs.shedlock:shedlock-micronaut:4.33.0"
    api("io.micronaut.reactor:micronaut-reactor")
    api("io.projectreactor.kotlin:reactor-kotlin-extensions")
    api("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    api("org.jetbrains.kotlinx:kotlinx-coroutines-reactive")
    api("software.amazon.awssdk:sqs")
    api("software.amazon.awssdk:sns")
    api("software.amazon.awssdk:ses")
    api("org.xhtmlrenderer:flying-saucer-core:9.3.1") // ao invés de 9.1.22
    api("org.xhtmlrenderer:flying-saucer-pdf-itext5:9.3.1") // ao invés de 9.1.22
    api("io.opentracing.brave:brave-opentracing:1.0.0")
    api("io.micronaut.tracing:micronaut-tracing-annotation")
    api("io.micronaut:micronaut-retry")
    api("com.github.jknack:handlebars:4.3.1")
    compileOnly("com.datadoghq:dd-trace-api:0.96.0")
    api("com.fasterxml.jackson.dataformat:jackson-dataformat-xml")
    api("com.fasterxml.jackson.dataformat:jackson-dataformat-csv")
    api("org.imgscalr:imgscalr-lib:4.2")
    api("org.bouncycastle:bcpkix-jdk18on:1.76") // ao invés de org.bouncycastle:bcpkix-jdk15on:1.70
    api("io.github.resilience4j:resilience4j-micronaut:2.0.0")
    api("io.github.resilience4j:resilience4j-kotlin:2.0.0")
    api("io.github.resilience4j:resilience4j-all:2.0.0")
    api("net.logstash.logback:logstash-logback-encoder:7.4")
    api("io.opentelemetry:opentelemetry-context")
    api("io.opentelemetry:opentelemetry-api")

    // dependencias exigidas em tempo de execução apenas
    runtimeOnly("io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0")
    runtimeOnly("io.opentelemetry:opentelemetry-extension-kotlin")
    runtimeOnly("io.micronaut.tracing:micronaut-tracing-opentelemetry")
    runtimeOnly("io.micronaut.tracing:micronaut-tracing-brave-http")
    runtimeOnly("io.micronaut.tracing:micronaut-tracing-opentelemetry-http")
    runtimeOnly("io.micronaut.cache:micronaut-cache-core")
    runtimeOnly("io.micronaut.redis:micronaut-redis-lettuce")

    // dependencias de teste
    testFixturesApi("ai.friday.morning:morning-date-mockk:0.9.8")
    testFixturesImplementation("io.mockk:mockk-jvm:1.13.8")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    testFixturesImplementation("io.micronaut.test:micronaut-test-kotest5")
    testFixturesImplementation("org.junit.jupiter:junit-jupiter")
    testFixturesImplementation("org.wiremock:wiremock:3.10.0")
    testFixturesImplementation("com.tngtech.archunit:archunit-junit5:1.1.0") // ao invés de 0.23.1
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit")
    testFixturesImplementation("org.testcontainers:testcontainers")
    api("software.amazon.awssdk:lambda")
}

application {
    mainClass.set("ai.friday.billpayment.ApplicationKt")
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions.jvmTarget = "17"
}

tasks.withType<Test> {
    retry {
        if (isCiServer) {
            maxRetries.set(2) // Retry up to 2 times
            maxFailures.set(10) // Allow up to 10 failures before stopping retries
        }
    }
}

tasks {
    compileKotlin {
        incremental = true
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
        }
    }
    compileTestKotlin {
        incremental = true
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
        }
    }

    jar {
        isZip64 = true
    }

    shadowJar {
        isZip64 = true
        enabled = false
    }

    test {
        jvmArgs =
            listOf("-Xmx2g", "-Djava.locale.providers=COMPAT,CLDR", "--add-opens", "java.base/java.util=ALL-UNNAMED")
        maxParallelForks = maxParallelForksValue
        useJUnitPlatform()
    }
}

micronaut {
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("ai.friday.billpayment.*")
    }
    /*testResources {
        enabled.set(true)
        sharedServer.set(true)
    }*/
    /*aot {
        // Please review carefully the optimizations enabled below
        // Check https://micronaut-projects.github.io/micronaut-aot/latest/guide/ for more details
        optimizeServiceLoading.set(false)
        convertYamlToJava.set(false)
        precomputeOperations.set(true)
        cacheEnvironment.set(true)
        optimizeClassLoading.set(true)
        deduceEnvironment.set(true)
        optimizeNetty.set(true)
        // configurationProperties.put("micronaut.security.jwks.enabled", "false")
    }*/
}

testlogger {
    theme = ThemeType.STANDARD_PARALLEL
    showPassed = false
    showSkipped = false
    showOnlySlow = false
}